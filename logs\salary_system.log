2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 15:35:08.546 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 15:35:10.125 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 15:35:10.125 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 15:35:10.125 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 15:35:10.140 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 15:35:10.140 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 15:35:10.140 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.140 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 15:35:10.140 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 15:35:10.140 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 15:35:10.140 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 15:35:10.140 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 15:35:10.408 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1518 | 菜单栏创建完成
2025-06-27 15:35:10.408 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 15:35:10.408 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 15:35:10.408 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.prototype_main_window:__init__:1494 | 菜单栏管理器初始化完成
2025-06-27 15:35:10.424 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2195 | 管理器设置完成，包含增强版表头管理器
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 15:35:10.439 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 15:35:10.439 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 15:35:10.445 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 15:35:10.445 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-06-27 15:35:10.445 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表']
2025-06-27 15:35:10.446 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 15:35:10.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 15:35:10.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 15:35:10.502 | INFO     | src.modules.data_import.config_sync_manager:_initialize_config:94 | 默认配置文件创建成功，包含4类工资表模板
2025-06-27 15:35:10.502 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:10.503 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 15:35:10.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 15:35:10.513 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 15:35:10.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:10.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 25.46ms
2025-06-27 15:35:10.534 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:10.546 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 15:35:10.602 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 15:35:10.641 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.642 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 15:35:10.650 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2168 | 快捷键设置完成
2025-06-27 15:35:10.657 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2157 | 主窗口UI设置完成。
2025-06-27 15:35:10.666 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2223 | 信号连接设置完成
2025-06-27 15:35:10.667 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:10.670 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2652 | 已加载字段映射信息，共0个表的映射
2025-06-27 15:35:10.670 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:10.671 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 15:35:10.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:10.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.19ms
2025-06-27 15:35:10.675 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:10.677 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:10.677 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:10.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 15:35:10.679 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:10.680 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.09ms
2025-06-27 15:35:10.680 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:10.681 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:10.681 | INFO     | src.gui.prototype.prototype_main_window:__init__:2109 | 原型主窗口初始化完成
2025-06-27 15:35:11.120 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 15:35:11.127 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 15:35:11.128 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1249 | MainWorkspaceArea 响应式适配: sm
2025-06-27 15:35:11.131 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 15:35:11.132 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 15:35:11.239 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 15:35:11.239 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 15:35:11.240 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 15:35:11.296 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 112 个匹配类型 'salary_data' 的表
2025-06-27 15:35:11.297 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 4 个月份
2025-06-27 15:35:11.297 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 15:35:11.299 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 15:35:11.300 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 15:35:11.300 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 15:35:11.301 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 15:35:11.302 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 15:35:11.302 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 15:35:11.303 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 15:35:11.303 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 15:35:11.304 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 15:35:11.304 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 15:35:11.304 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 28 个月份
2025-06-27 15:35:11.305 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 15:35:11.308 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 15:35:11.508 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 15:35:11.508 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 15:35:11.552 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 112 个匹配类型 'salary_data' 的表
2025-06-27 15:35:11.570 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.570 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.573 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.574 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.577 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:11.577 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:11.578 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.01ms
2025-06-27 15:35:11.580 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:11.580 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2743 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 15:35:11.581 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:11.582 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 15:35:11.582 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_04_active_employees，第1页，每页50条
2025-06-27 15:35:11.582 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_04_active_employees 第1页
2025-06-27 15:35:11.583 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_04_active_employees 第1页数据，每页50条
2025-06-27 15:35:11.584 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:35:11.598 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 15:35:11.613 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:35:11.615 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_active_employees 没有字段映射配置
2025-06-27 15:35:11.615 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 15:35:11.638 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 15:35:11.645 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:35:11.646 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:11.657 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 15:35:11.672 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_active_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:11.678 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:35:11.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 15:35:11.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:35:11.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:35:11.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 82.26ms
2025-06-27 15:35:11.765 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:35:11.765 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 15:35:28.489 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月', '工资表 > 2027年', '工资表 > 2025年', '工资表']
2025-06-27 15:35:28.489 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月
2025-06-27 15:35:28.489 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:28.489 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:28.489 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:28.489 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:28.489 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:28.489 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:28.489 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 16.17ms
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:28.505 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:28.505 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:28.520 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.20ms
2025-06-27 15:35:28.520 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:28.520 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:28.520 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年', '工资表']
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > A岗职工
2025-06-27 15:35:30.149 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:30.149 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:30.149 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:30.149 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:30.149 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:30.149 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_04_a_grade_employees，第1页，每页50条
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_04_a_grade_employees 第1页
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > A岗职工
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_04_a_grade_employees 第1页数据，每页50条
2025-06-27 15:35:30.149 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:35:30.149 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_a_grade_employees 没有字段映射配置
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 15:35:30.165 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:35:30.165 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:35:30.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 15:35:30.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:35:30.189 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:35:30.191 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:35:30.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 45.28ms
2025-06-27 15:35:30.227 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:35:30.227 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 15:35:38.806 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 15:35:38.806 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:38.806 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:35:38.806 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:35:38.806 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:35:38.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 12
2025-06-27 15:35:38.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-27 15:35:38.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-27 15:35:38.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 15.64ms
2025-06-27 15:35:38.839 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-27 15:35:38.840 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 全部在职人员', '工资表 > 2027年']
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 15:35:41.081 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:41.081 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:41.081 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:41.081 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:41.081 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:41.081 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_04_active_employees，第1页，每页50条
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_04_active_employees 第1页
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_active_employees 没有字段映射配置
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 15:35:41.081 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_active_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:35:41.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 12 -> 50
2025-06-27 15:35:41.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:35:41.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:35:41.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 39.14ms
2025-06-27 15:35:41.125 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:35:41.144 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 15:35:41.150 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 15:35:51.369 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 退休人员', '工资表 > 2027年 > 4月 > 全部在职人员']
2025-06-27 15:35:51.369 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 退休人员
2025-06-27 15:35:51.371 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:51.371 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:51.372 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:51.372 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:51.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.01ms
2025-06-27 15:35:51.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:51.374 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:51.375 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 15:35:51.375 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 退休人员
2025-06-27 15:35:51.376 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_pension_employees 获取数据...
2025-06-27 15:35:51.378 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_pension_employees 获取 13 行数据。
2025-06-27 15:35:51.381 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_pension_employees 没有字段映射配置
2025-06-27 15:35:51.382 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:51.383 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:51.395 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 15:35:51.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 15:35:51.422 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 15:35:51.424 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 15:35:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 30.33ms
2025-06-27 15:35:51.435 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 离休人员', '工资表 > 2027年 > 4月 > 退休人员']
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:35:58.768 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:58.768 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:58.768 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:58.768 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:58.768 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:58.768 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:35:58.768 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_retired_employees 获取数据...
2025-06-27 15:35:58.768 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_retired_employees 获取 2 行数据。
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_retired_employees 没有字段映射配置
2025-06-27 15:35:58.768 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 15:35:58.784 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 0.00ms
2025-06-27 15:35:58.801 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 15:36:10.811 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-27 15:36:10.811 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2455 | 接收到数据导入请求，推断的目标路径: 工资表 > 2027年 > 4月 > 离休人员。打开导入对话框。
2025-06-27 15:36:10.811 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-27 15:36:10.811 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:36:10.811 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-27 15:36:10.826 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-06-27 15:36:10.842 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:36:10.908 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 112 个匹配类型 'salary_data' 的表
2025-06-27 15:36:11.000 | INFO     | src.gui.dialogs:_get_template_fields:1884 | 使用字段模板: 全部在职人员工资表
2025-06-27 15:36:11.091 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-27 15:36:11.092 | INFO     | src.gui.dialogs:_on_import_mode_changed:2663 | 切换到单Sheet导入模式
2025-06-27 15:36:11.093 | INFO     | src.gui.dialogs:_apply_default_settings:2206 | 已应用默认设置: {'start_row': 1, 'import_mode': 'single_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'merge_to_single_table', 'table_template': 'salary_data'}
2025-06-27 15:36:11.094 | INFO     | src.gui.dialogs:_setup_tooltips:2461 | 工具提示设置完成
2025-06-27 15:36:11.095 | INFO     | src.gui.dialogs:_setup_shortcuts:2500 | 快捷键设置完成
2025-06-27 15:36:11.097 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-27 15:36:15.425 | INFO     | src.gui.dialogs:_on_target_changed:2145 | 目标位置已更新: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 15:36:17.988 | INFO     | src.gui.dialogs:_on_target_changed:2145 | 目标位置已更新: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:36:27.336 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:36:27.553 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:36:27.554 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-27 15:36:27.555 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2241 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-27 15:36:37.060 | INFO     | src.gui.dialogs:_on_import_mode_changed:2657 | 切换到多Sheet导入模式
2025-06-27 15:36:42.127 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-27 15:36:42.128 | ERROR    | src.gui.dialogs:_show_settings:2555 | 显示设置对话框失败: name 'QWidget' is not defined
2025-06-27 15:36:44.983 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.118 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.334 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.354 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.355 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.357 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.591 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-27 15:37:21.737 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-27 15:37:21.737 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 16 个字段
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 离休人员工资表 模板生成字段映射
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_retired_employees
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_retired_employees 生成标准化字段映射: 16 个字段
2025-06-27 15:37:21.753 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-27 15:37:21.768 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-27 15:37:21.768 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_retired_employees 不存在，将根据模板创建...
2025-06-27 15:37:21.800 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。
2025-06-27 15:37:21.821 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.822 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.822 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-27 15:37:21.941 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-27 15:37:21.941 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 27 个字段
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 退休人员工资表 模板生成字段映射
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_pension_employees
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_pension_employees 生成标准化字段映射: 27 个字段
2025-06-27 15:37:21.941 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-27 15:37:21.959 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-27 15:37:21.959 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 15:37:21.959 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_pension_employees 不存在，将根据模板创建...
2025-06-27 15:37:21.972 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。
2025-06-27 15:37:21.972 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.972 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.972 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-27 15:37:22.145 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-27 15:37:22.145 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 23 个字段
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 全部在职人员工资表 模板生成字段映射
2025-06-27 15:37:22.160 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_active_employees
2025-06-27 15:37:22.161 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_active_employees 生成标准化字段映射: 23 个字段
2025-06-27 15:37:22.161 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-27 15:37:22.161 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-27 15:37:22.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 15:37:22.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_active_employees 不存在，将根据模板创建...
2025-06-27 15:37:22.210 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。
2025-06-27 15:37:22.217 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:22.217 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:22.217 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:22.359 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-27 15:37:22.363 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:22.363 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-27 15:37:22.366 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-27 15:37:22.366 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-27 15:37:22.367 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-27 15:37:22.369 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 21 个字段
2025-06-27 15:37:22.370 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 A岗职工 模板生成字段映射
2025-06-27 15:37:22.378 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_a_grade_employees
2025-06-27 15:37:22.378 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_a_grade_employees 生成标准化字段映射: 21 个字段
2025-06-27 15:37:22.378 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet A岗职工 存在 2 个验证错误
2025-06-27 15:37:22.383 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet A岗职工 数据处理完成: 62 行
2025-06-27 15:37:22.384 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 15:37:22.386 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_a_grade_employees 不存在，将根据模板创建...
2025-06-27 15:37:22.406 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。
2025-06-27 15:37:22.432 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-27 15:37:22.435 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-05', 'data_description': '2027年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 5月 > 全部在职人员'}
2025-06-27 15:37:22.443 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2468 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-05', 'data_description': '2027年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 5月 > 全部在职人员'}
2025-06-27 15:37:22.446 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2479 | 导入模式: multi_sheet, 目标路径: '工资表 > 2027年 > 5月 > 全部在职人员'
2025-06-27 15:37:22.447 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2487 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:22.447 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2545 | 检查是否需要更新导航面板: ['工资表', '2027年', '5月', '全部在职人员']
2025-06-27 15:37:22.448 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2549 | 检测到工资数据导入，开始刷新导航面板
2025-06-27 15:37:22.448 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2553 | 使用强制刷新方法
2025-06-27 15:37:22.449 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 15:37:22.450 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 15:37:22.453 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 离休人员', '工资表 > 2027年 > 4月 > 退休人员']
2025-06-27 15:37:22.461 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:37:22.467 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:37:22.468 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:37:22.468 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 15:37:22.469 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 15:37:22.494 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 25.07ms
2025-06-27 15:37:22.497 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:37:22.502 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:37:22.504 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 15:37:22.505 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:37:22.507 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_retired_employees 获取数据...
2025-06-27 15:37:22.517 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_retired_employees 获取 2 行数据。
2025-06-27 15:37:22.519 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_retired_employees 没有字段映射配置
2025-06-27 15:37:22.521 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:22.522 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 15:37:22.571 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 15:37:22.598 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 5 个月份
2025-06-27 15:37:22.613 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 15:37:22.623 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 15:37:22.623 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 15:37:22.623 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 15:37:22.626 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 15:37:22.627 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 15:37:22.628 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 15:37:22.628 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 29 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 15:37:22.637 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2558 | 将在800ms后导航到: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:22.725 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 15:37:22.735 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 15:37:22.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 2
2025-06-27 15:37:22.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 15:37:22.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 15:37:22.783 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 53.67ms
2025-06-27 15:37:22.784 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 15:37:23.438 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2591 | 尝试导航到新导入的路径: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.446 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 4月 > 离休人员']
2025-06-27 15:37:23.447 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.450 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:37:23.451 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:37:23.452 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2902115707664 已经注册，将覆盖现有注册
2025-06-27 15:37:23.452 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2902115708240 已经注册，将覆盖现有注册
2025-06-27 15:37:23.452 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 15:37:23.453 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 15:37:23.454 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.04ms
2025-06-27 15:37:23.454 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:37:23.455 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:37:23.456 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 15:37:23.458 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 15:37:23.459 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_active_employees 第1页
2025-06-27 15:37:23.460 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.460 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_active_employees 第1页数据，每页50条
2025-06-27 15:37:23.461 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:37:23.465 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2596 | 已成功导航到新导入的路径: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.471 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 15:37:23.476 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:23.480 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 15:37:23.487 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 15:37:23.501 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 15:37:23.513 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:23.514 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:23.516 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 15:37:23.523 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 15:37:23.525 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:37:23.556 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 50
2025-06-27 15:37:23.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:37:23.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:23.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:37:23.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 55.73ms
2025-06-27 15:37:23.609 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:37:23.610 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 15:37:36.765 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 15:37:36.765 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:36.765 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:36.765 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:36.780 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:37:36.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 18.16ms
2025-06-27 15:37:36.823 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 15:37:36.824 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 15:37:41.719 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年']
2025-06-27 15:37:41.719 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2902115707664 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2902115708240 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 15:37:41.719 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:507 | 检测到表格 table_1_2902012469776 的表头重影: ['工号', 'id']
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:_clear_single_table_header:196 | 检测到表格 table_1_2902012469776 存在表头重影: ['工号', 'id']
2025-06-27 15:37:41.719 | WARNING  | src.gui.prototype.prototype_main_window:_on_header_shadow_detected:3469 | 检测到表格 table_1_2902012469776 表头重影: ['工号', 'id']
2025-06-27 15:37:41.862 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:519 | 表格 table_1_2902012469776 表头重影修复成功
2025-06-27 15:37:41.863 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 144.20ms
2025-06-27 15:37:41.863 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：1 个重影表格，1 个修复成功，0 个修复失败
2025-06-27 15:37:41.865 | INFO     | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3393 | 导航切换预清理：修复了 1 个表格的表头重影
2025-06-27 15:37:41.866 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:37:41.866 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 15:37:41.867 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_a_grade_employees，第1页，每页50条
2025-06-27 15:37:41.867 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_a_grade_employees 第1页
2025-06-27 15:37:41.868 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 15:37:41.868 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_a_grade_employees 第1页数据，每页50条
2025-06-27 15:37:41.869 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:37:41.872 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 15:37:41.874 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:41.875 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 15:37:41.876 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 15:37:41.879 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 15:37:41.893 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:41.895 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:41.896 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 15:37:41.897 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:37:41.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 15:37:41.907 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:37:41.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:37:41.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:41.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:37:41.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 84.97ms
2025-06-27 15:37:41.986 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:37:41.986 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 15:37:45.129 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 15:37:45.129 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:45.129 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:45.129 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:45.129 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 12
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 15.66ms
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-27 15:37:45.144 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 15:37:55.818 | INFO     | __main__:main:302 | 应用程序正常退出
