2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 15:35:08.531 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 15:35:08.546 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 15:35:10.125 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 15:35:10.125 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 15:35:10.125 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 15:35:10.125 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 15:35:10.140 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 15:35:10.140 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 15:35:10.140 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.140 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 15:35:10.140 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 15:35:10.140 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 15:35:10.140 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 15:35:10.140 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 15:35:10.408 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1518 | 菜单栏创建完成
2025-06-27 15:35:10.408 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 15:35:10.408 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 15:35:10.408 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.prototype_main_window:__init__:1494 | 菜单栏管理器初始化完成
2025-06-27 15:35:10.424 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2195 | 管理器设置完成，包含增强版表头管理器
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 15:35:10.424 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 15:35:10.439 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 15:35:10.439 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 15:35:10.445 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 15:35:10.445 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-06-27 15:35:10.445 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表']
2025-06-27 15:35:10.446 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 15:35:10.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 15:35:10.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 15:35:10.502 | INFO     | src.modules.data_import.config_sync_manager:_initialize_config:94 | 默认配置文件创建成功，包含4类工资表模板
2025-06-27 15:35:10.502 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:10.503 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 15:35:10.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 15:35:10.513 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 15:35:10.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:10.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 25.46ms
2025-06-27 15:35:10.534 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:10.546 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 15:35:10.602 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 15:35:10.641 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 15:35:10.642 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 15:35:10.650 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2168 | 快捷键设置完成
2025-06-27 15:35:10.657 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2157 | 主窗口UI设置完成。
2025-06-27 15:35:10.666 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2223 | 信号连接设置完成
2025-06-27 15:35:10.667 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:10.670 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2652 | 已加载字段映射信息，共0个表的映射
2025-06-27 15:35:10.670 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:10.671 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 15:35:10.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:10.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.19ms
2025-06-27 15:35:10.675 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:10.677 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:10.677 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:10.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 15:35:10.679 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:10.680 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.09ms
2025-06-27 15:35:10.680 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:10.681 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:10.681 | INFO     | src.gui.prototype.prototype_main_window:__init__:2109 | 原型主窗口初始化完成
2025-06-27 15:35:11.120 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 15:35:11.127 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 15:35:11.128 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1249 | MainWorkspaceArea 响应式适配: sm
2025-06-27 15:35:11.131 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 15:35:11.132 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 15:35:11.239 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 15:35:11.239 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 15:35:11.240 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 15:35:11.296 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 112 个匹配类型 'salary_data' 的表
2025-06-27 15:35:11.297 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 4 个月份
2025-06-27 15:35:11.297 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 15:35:11.299 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 15:35:11.300 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 15:35:11.300 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 15:35:11.301 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 15:35:11.302 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 15:35:11.302 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 15:35:11.303 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 15:35:11.303 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 15:35:11.304 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 15:35:11.304 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 15:35:11.304 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 28 个月份
2025-06-27 15:35:11.305 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 15:35:11.308 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 15:35:11.508 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 15:35:11.508 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 15:35:11.552 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 112 个匹配类型 'salary_data' 的表
2025-06-27 15:35:11.570 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.570 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.573 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.574 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 04月 > 全部在职人员
2025-06-27 15:35:11.577 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:11.577 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:11.578 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.01ms
2025-06-27 15:35:11.580 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:11.580 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2743 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 15:35:11.581 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:11.582 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 15:35:11.582 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_04_active_employees，第1页，每页50条
2025-06-27 15:35:11.582 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_04_active_employees 第1页
2025-06-27 15:35:11.583 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_04_active_employees 第1页数据，每页50条
2025-06-27 15:35:11.584 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:35:11.598 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 15:35:11.613 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:35:11.615 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_active_employees 没有字段映射配置
2025-06-27 15:35:11.615 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 15:35:11.638 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 15:35:11.645 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:35:11.646 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:11.657 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 15:35:11.672 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_active_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:11.678 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:35:11.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 15:35:11.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:35:11.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:35:11.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 82.26ms
2025-06-27 15:35:11.765 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:35:11.765 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 15:35:28.489 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月', '工资表 > 2027年', '工资表 > 2025年', '工资表']
2025-06-27 15:35:28.489 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月
2025-06-27 15:35:28.489 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:28.489 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:28.489 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:28.489 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:28.489 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:28.489 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:28.489 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 16.17ms
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:28.505 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:28.505 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 15:35:28.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 15:35:28.520 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.20ms
2025-06-27 15:35:28.520 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 15:35:28.520 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:28.520 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年', '工资表']
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > A岗职工
2025-06-27 15:35:30.149 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:30.149 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:30.149 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:30.149 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:30.149 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:30.149 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_04_a_grade_employees，第1页，每页50条
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_04_a_grade_employees 第1页
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > A岗职工
2025-06-27 15:35:30.149 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_04_a_grade_employees 第1页数据，每页50条
2025-06-27 15:35:30.149 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:35:30.149 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_a_grade_employees 没有字段映射配置
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 15:35:30.165 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:35:30.165 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:30.165 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:35:30.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 15:35:30.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:35:30.189 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:35:30.191 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:35:30.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 45.28ms
2025-06-27 15:35:30.227 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:35:30.227 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 15:35:38.806 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 15:35:38.806 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:38.806 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:35:38.806 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:35:38.806 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:35:38.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 12
2025-06-27 15:35:38.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-27 15:35:38.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-27 15:35:38.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 15.64ms
2025-06-27 15:35:38.839 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-27 15:35:38.840 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 全部在职人员', '工资表 > 2027年']
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 15:35:41.081 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:41.081 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:41.081 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:41.081 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:41.081 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:41.081 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_04_active_employees，第1页，每页50条
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_04_active_employees 第1页
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_active_employees 没有字段映射配置
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 15:35:41.081 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_active_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:41.081 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:35:41.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 12 -> 50
2025-06-27 15:35:41.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:35:41.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:35:41.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 39.14ms
2025-06-27 15:35:41.125 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:35:41.144 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 15:35:41.150 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 15:35:51.369 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 退休人员', '工资表 > 2027年 > 4月 > 全部在职人员']
2025-06-27 15:35:51.369 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 退休人员
2025-06-27 15:35:51.371 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:51.371 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:51.372 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:51.372 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:51.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.01ms
2025-06-27 15:35:51.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:51.374 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:51.375 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 15:35:51.375 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 退休人员
2025-06-27 15:35:51.376 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_pension_employees 获取数据...
2025-06-27 15:35:51.378 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_pension_employees 获取 13 行数据。
2025-06-27 15:35:51.381 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_pension_employees 没有字段映射配置
2025-06-27 15:35:51.382 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:51.383 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:51.395 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 15:35:51.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 15:35:51.422 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 15:35:51.424 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 15:35:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 30.33ms
2025-06-27 15:35:51.435 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 离休人员', '工资表 > 2027年 > 4月 > 退休人员']
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:35:58.768 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:35:58.768 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 15:35:58.768 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 15:35:58.768 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 15:35:58.768 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:35:58.768 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:35:58.768 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_retired_employees 获取数据...
2025-06-27 15:35:58.768 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_retired_employees 获取 2 行数据。
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_retired_employees 没有字段映射配置
2025-06-27 15:35:58.768 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:35:58.768 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 15:35:58.784 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 15:35:58.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 0.00ms
2025-06-27 15:35:58.801 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 15:36:10.811 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-27 15:36:10.811 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2455 | 接收到数据导入请求，推断的目标路径: 工资表 > 2027年 > 4月 > 离休人员。打开导入对话框。
2025-06-27 15:36:10.811 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-27 15:36:10.811 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:36:10.811 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-27 15:36:10.826 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-06-27 15:36:10.842 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:36:10.908 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 112 个匹配类型 'salary_data' 的表
2025-06-27 15:36:11.000 | INFO     | src.gui.dialogs:_get_template_fields:1884 | 使用字段模板: 全部在职人员工资表
2025-06-27 15:36:11.091 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-27 15:36:11.092 | INFO     | src.gui.dialogs:_on_import_mode_changed:2663 | 切换到单Sheet导入模式
2025-06-27 15:36:11.093 | INFO     | src.gui.dialogs:_apply_default_settings:2206 | 已应用默认设置: {'start_row': 1, 'import_mode': 'single_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'merge_to_single_table', 'table_template': 'salary_data'}
2025-06-27 15:36:11.094 | INFO     | src.gui.dialogs:_setup_tooltips:2461 | 工具提示设置完成
2025-06-27 15:36:11.095 | INFO     | src.gui.dialogs:_setup_shortcuts:2500 | 快捷键设置完成
2025-06-27 15:36:11.097 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-27 15:36:15.425 | INFO     | src.gui.dialogs:_on_target_changed:2145 | 目标位置已更新: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 15:36:17.988 | INFO     | src.gui.dialogs:_on_target_changed:2145 | 目标位置已更新: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:36:27.336 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:36:27.553 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:36:27.554 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-27 15:36:27.555 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2241 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-27 15:36:37.060 | INFO     | src.gui.dialogs:_on_import_mode_changed:2657 | 切换到多Sheet导入模式
2025-06-27 15:36:42.127 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-27 15:36:42.128 | ERROR    | src.gui.dialogs:_show_settings:2555 | 显示设置对话框失败: name 'QWidget' is not defined
2025-06-27 15:36:44.983 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.118 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.334 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.354 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.355 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.357 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.575 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.591 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-27 15:37:21.737 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-27 15:37:21.737 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-27 15:37:21.737 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 16 个字段
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 离休人员工资表 模板生成字段映射
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_retired_employees
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_retired_employees 生成标准化字段映射: 16 个字段
2025-06-27 15:37:21.753 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-27 15:37:21.753 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-27 15:37:21.768 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-27 15:37:21.768 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_retired_employees 不存在，将根据模板创建...
2025-06-27 15:37:21.800 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。
2025-06-27 15:37:21.821 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.822 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.822 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-27 15:37:21.941 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-27 15:37:21.941 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 27 个字段
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 退休人员工资表 模板生成字段映射
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_pension_employees
2025-06-27 15:37:21.941 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_pension_employees 生成标准化字段映射: 27 个字段
2025-06-27 15:37:21.941 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-27 15:37:21.959 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-27 15:37:21.959 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 15:37:21.959 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_pension_employees 不存在，将根据模板创建...
2025-06-27 15:37:21.972 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。
2025-06-27 15:37:21.972 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:21.972 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:21.972 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-27 15:37:22.145 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-27 15:37:22.145 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 23 个字段
2025-06-27 15:37:22.145 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 全部在职人员工资表 模板生成字段映射
2025-06-27 15:37:22.160 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_active_employees
2025-06-27 15:37:22.161 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_active_employees 生成标准化字段映射: 23 个字段
2025-06-27 15:37:22.161 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-27 15:37:22.161 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-27 15:37:22.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 15:37:22.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_active_employees 不存在，将根据模板创建...
2025-06-27 15:37:22.210 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。
2025-06-27 15:37:22.217 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 15:37:22.217 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 15:37:22.217 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 15:37:22.359 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-27 15:37:22.363 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 15:37:22.363 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-27 15:37:22.366 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-27 15:37:22.366 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-27 15:37:22.367 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-27 15:37:22.369 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 21 个字段
2025-06-27 15:37:22.370 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 A岗职工 模板生成字段映射
2025-06-27 15:37:22.378 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_05_a_grade_employees
2025-06-27 15:37:22.378 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_05_a_grade_employees 生成标准化字段映射: 21 个字段
2025-06-27 15:37:22.378 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet A岗职工 存在 2 个验证错误
2025-06-27 15:37:22.383 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet A岗职工 数据处理完成: 62 行
2025-06-27 15:37:22.384 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 15:37:22.386 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_05_a_grade_employees 不存在，将根据模板创建...
2025-06-27 15:37:22.406 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。
2025-06-27 15:37:22.432 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-27 15:37:22.435 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-05', 'data_description': '2027年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 5月 > 全部在职人员'}
2025-06-27 15:37:22.443 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2468 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-05', 'data_description': '2027年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 5月 > 全部在职人员'}
2025-06-27 15:37:22.446 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2479 | 导入模式: multi_sheet, 目标路径: '工资表 > 2027年 > 5月 > 全部在职人员'
2025-06-27 15:37:22.447 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2487 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:22.447 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2545 | 检查是否需要更新导航面板: ['工资表', '2027年', '5月', '全部在职人员']
2025-06-27 15:37:22.448 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2549 | 检测到工资数据导入，开始刷新导航面板
2025-06-27 15:37:22.448 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2553 | 使用强制刷新方法
2025-06-27 15:37:22.449 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 15:37:22.450 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 15:37:22.453 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 4月 > 离休人员', '工资表 > 2027年 > 4月 > 退休人员']
2025-06-27 15:37:22.461 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:37:22.467 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:37:22.468 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:37:22.468 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 15:37:22.469 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 15:37:22.494 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 25.07ms
2025-06-27 15:37:22.497 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:37:22.502 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:37:22.504 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 15:37:22.505 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 15:37:22.507 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_retired_employees 获取数据...
2025-06-27 15:37:22.517 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_retired_employees 获取 2 行数据。
2025-06-27 15:37:22.519 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_04_retired_employees 没有字段映射配置
2025-06-27 15:37:22.521 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:22.522 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 15:37:22.571 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 15:37:22.598 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 5 个月份
2025-06-27 15:37:22.613 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 15:37:22.623 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 15:37:22.623 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 15:37:22.623 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 15:37:22.626 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 15:37:22.627 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 15:37:22.628 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 15:37:22.628 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 29 个月份
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 15:37:22.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 15:37:22.637 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2558 | 将在800ms后导航到: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:22.725 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 15:37:22.735 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 15:37:22.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 2
2025-06-27 15:37:22.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 15:37:22.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 15:37:22.783 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 53.67ms
2025-06-27 15:37:22.784 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 15:37:23.438 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2591 | 尝试导航到新导入的路径: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.446 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 4月 > 离休人员']
2025-06-27 15:37:23.447 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.450 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:37:23.451 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:37:23.452 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2902115707664 已经注册，将覆盖现有注册
2025-06-27 15:37:23.452 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2902115708240 已经注册，将覆盖现有注册
2025-06-27 15:37:23.452 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 15:37:23.453 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 15:37:23.454 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.04ms
2025-06-27 15:37:23.454 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 15:37:23.455 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:37:23.456 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 15:37:23.458 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 15:37:23.459 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_active_employees 第1页
2025-06-27 15:37:23.460 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.460 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_active_employees 第1页数据，每页50条
2025-06-27 15:37:23.461 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:37:23.465 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2596 | 已成功导航到新导入的路径: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 15:37:23.471 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 15:37:23.476 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:23.480 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 15:37:23.487 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 15:37:23.501 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 15:37:23.513 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:23.514 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:23.516 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 15:37:23.523 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 15:37:23.525 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:37:23.556 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 50
2025-06-27 15:37:23.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:37:23.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:23.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:37:23.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 55.73ms
2025-06-27 15:37:23.609 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:37:23.610 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 15:37:36.765 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 15:37:36.765 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:36.765 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:36.765 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:36.780 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:36.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:37:36.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 18.16ms
2025-06-27 15:37:36.823 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 15:37:36.824 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 15:37:41.719 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > A岗职工', '工资表 > 2027年 > 4月', '工资表 > 2025年', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年']
2025-06-27 15:37:41.719 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2902012674992 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2902012469776 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2902115707664 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2902115708240 已经注册，将覆盖现有注册
2025-06-27 15:37:41.719 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 15:37:41.719 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:507 | 检测到表格 table_1_2902012469776 的表头重影: ['工号', 'id']
2025-06-27 15:37:41.719 | WARNING  | src.gui.table_header_manager:_clear_single_table_header:196 | 检测到表格 table_1_2902012469776 存在表头重影: ['工号', 'id']
2025-06-27 15:37:41.719 | WARNING  | src.gui.prototype.prototype_main_window:_on_header_shadow_detected:3469 | 检测到表格 table_1_2902012469776 表头重影: ['工号', 'id']
2025-06-27 15:37:41.862 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:519 | 表格 table_1_2902012469776 表头重影修复成功
2025-06-27 15:37:41.863 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 144.20ms
2025-06-27 15:37:41.863 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：1 个重影表格，1 个修复成功，0 个修复失败
2025-06-27 15:37:41.865 | INFO     | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3393 | 导航切换预清理：修复了 1 个表格的表头重影
2025-06-27 15:37:41.866 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 15:37:41.866 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 15:37:41.867 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_a_grade_employees，第1页，每页50条
2025-06-27 15:37:41.867 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_a_grade_employees 第1页
2025-06-27 15:37:41.868 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 15:37:41.868 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_a_grade_employees 第1页数据，每页50条
2025-06-27 15:37:41.869 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 15:37:41.872 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 15:37:41.874 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:41.875 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 15:37:41.876 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 15:37:41.879 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 15:37:41.893 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:41.895 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:41.896 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 15:37:41.897 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 15:37:41.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 15:37:41.907 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:37:41.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 15:37:41.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:41.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 15:37:41.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 84.97ms
2025-06-27 15:37:41.986 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 15:37:41.986 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 15:37:45.129 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 15:37:45.129 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 15:37:45.129 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 15:37:45.129 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 15:37:45.129 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 12
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 15.66ms
2025-06-27 15:37:45.144 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-27 15:37:45.144 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 15:37:55.818 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-27 16:03:34.003 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 16:03:34.003 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 16:03:34.003 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 16:03:34.003 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 16:03:34.003 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 16:03:34.003 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 16:03:36.980 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 16:03:36.980 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 16:03:36.980 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:03:36.980 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:03:36.980 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 16:03:36.995 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 16:03:36.996 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 16:03:37.061 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 16:03:37.063 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 16:03:37.064 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:03:37.065 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 16:03:37.065 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 16:03:37.110 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 16:03:37.117 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 16:03:37.117 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 16:03:37.850 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1518 | 菜单栏创建完成
2025-06-27 16:03:37.850 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:03:37.850 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:03:37.850 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 16:03:37.866 | INFO     | src.gui.prototype.prototype_main_window:__init__:1494 | 菜单栏管理器初始化完成
2025-06-27 16:03:37.866 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 16:03:37.866 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2195 | 管理器设置完成，包含增强版表头管理器
2025-06-27 16:03:37.900 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-27 16:03:37.901 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 16:03:37.959 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 16:03:37.993 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 16:03:37.994 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 16:03:38.030 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 16:03:38.033 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 6个展开项
2025-06-27 16:03:38.034 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-06-27 16:03:38.035 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 16:03:38.166 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 16:03:38.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 16:03:38.170 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:03:38.171 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 16:03:38.176 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 16:03:38.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:03:38.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:03:38.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.69ms
2025-06-27 16:03:38.186 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:03:38.200 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 16:03:38.288 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 16:03:38.345 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:03:38.345 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:03:38.347 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2168 | 快捷键设置完成
2025-06-27 16:03:38.347 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2157 | 主窗口UI设置完成。
2025-06-27 16:03:38.347 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2223 | 信号连接设置完成
2025-06-27 16:03:38.349 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:03:38.351 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2652 | 已加载字段映射信息，共4个表的映射
2025-06-27 16:03:38.351 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:03:38.352 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:03:38.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:03:38.354 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.55ms
2025-06-27 16:03:38.355 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:03:38.357 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:03:38.357 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:03:38.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:03:38.359 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:03:38.360 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.12ms
2025-06-27 16:03:38.360 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:03:38.361 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:03:38.362 | INFO     | src.gui.prototype.prototype_main_window:__init__:2109 | 原型主窗口初始化完成
2025-06-27 16:03:38.913 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 16:03:38.918 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 16:03:38.919 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 16:03:38.920 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 16:03:38.920 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1249 | MainWorkspaceArea 响应式适配: sm
2025-06-27 16:03:38.924 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 16:03:38.924 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 16:03:38.926 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 16:03:39.587 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 16:03:39.589 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 5 个月份
2025-06-27 16:03:39.589 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 16:03:39.591 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 16:03:39.592 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 16:03:39.592 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 16:03:39.593 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 16:03:39.593 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 16:03:39.594 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 16:03:39.595 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 16:03:39.595 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 16:03:39.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 16:03:39.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 16:03:39.598 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 29 个月份
2025-06-27 16:03:39.599 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 16:03:39.599 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 16:03:39.801 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 16:03:39.801 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 16:03:39.849 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 16:03:39.855 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:03:39.855 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:03:39.855 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:03:39.860 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:03:39.862 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:03:39.863 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:03:39.864 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.03ms
2025-06-27 16:03:39.865 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:03:39.865 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2743 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 16:03:39.867 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:03:39.867 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:03:39.867 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:03:39.867 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_active_employees 第1页
2025-06-27 16:03:39.883 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_active_employees 第1页数据，每页50条
2025-06-27 16:03:39.884 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:03:39.909 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 16:03:39.912 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:03:39.927 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 16:03:39.929 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 16:03:39.931 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 16:03:39.945 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:03:39.946 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:03:39.965 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:03:39.968 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:03:39.980 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:03:39.986 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 16:03:39.986 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:03:39.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:03:39.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:03:40.063 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 80.16ms
2025-06-27 16:03:40.078 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:03:40.078 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:03:57.169 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-27 16:03:57.169 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-27 16:04:36.877 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 16:04:36.877 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:04:36.877 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:04:36.877 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:04:36.877 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:04:36.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:04:36.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:04:36.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:04:36.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:04:36.914 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.76ms
2025-06-27 16:04:36.914 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 16:04:36.914 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 16:15:37.161 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 16:15:37.162 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 16:15:37.162 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 16:15:37.162 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 16:15:37.164 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 16:15:37.164 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 16:15:37.962 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:15:37.976 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:15:37.976 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:15:37.977 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 16:15:37.977 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 16:15:37.978 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 16:15:37.997 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 16:15:37.997 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 16:15:37.997 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:15:37.997 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 16:16:50.615 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 16:16:50.615 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 16:16:50.615 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 16:16:50.616 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 16:16:50.616 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 16:16:50.617 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 16:16:51.426 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:16:51.426 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:16:51.427 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:16:51.428 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 16:16:51.429 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 16:16:51.430 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 16:16:51.440 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 16:16:51.441 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 16:16:51.441 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:16:51.442 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 16:16:51.452 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2027_05_active_employees
2025-06-27 16:16:51.464 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2027_05_a_grade_employees
2025-06-27 16:16:51.477 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2027_05_pension_employees
2025-06-27 16:16:51.477 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2027_05_retired_employees
2025-06-27 16:17:32.295 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 16:17:32.296 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 16:17:32.296 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 16:17:32.296 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 16:17:32.297 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 16:17:32.297 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 16:17:33.136 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:17:33.144 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:17:33.144 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:17:33.145 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 16:17:33.145 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 16:17:33.146 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 16:17:33.155 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 16:17:33.155 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 16:17:33.157 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:17:33.157 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 16:32:35.351 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 16:32:35.351 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月
2025-06-27 16:32:35.351 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:32:35.351 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:32:35.351 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:32:35.351 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:32:35.351 | WARNING  | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:507 | 检测到表格 table_1_1491289383440 的表头重影: ['工号', 'id']
2025-06-27 16:32:35.351 | WARNING  | src.gui.table_header_manager:_clear_single_table_header:196 | 检测到表格 table_1_1491289383440 存在表头重影: ['工号', 'id']
2025-06-27 16:32:35.351 | WARNING  | src.gui.prototype.prototype_main_window:_on_header_shadow_detected:3469 | 检测到表格 table_1_1491289383440 表头重影: ['工号', 'id']
2025-06-27 16:32:35.452 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:519 | 表格 table_1_1491289383440 表头重影修复成功
2025-06-27 16:32:35.452 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 101.35ms
2025-06-27 16:32:35.452 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：1 个重影表格，1 个修复成功，0 个修复失败
2025-06-27 16:32:35.452 | INFO     | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3393 | 导航切换预清理：修复了 1 个表格的表头重影
2025-06-27 16:32:35.452 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:32:35.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:32:35.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:32:35.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:32:35.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 17.64ms
2025-06-27 16:32:35.470 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:32:35.470 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:32:35.470 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:32:35.483 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:32:35.483 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:32:35.483 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:32:35.483 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 13.73ms
2025-06-27 16:32:35.483 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:32:35.483 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:32:35.483 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月
2025-06-27 16:32:37.380 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 16:32:37.380 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:32:37.380 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:32:37.380 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:32:37.380 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:32:37.380 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:32:37.380 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:32:37.380 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:32:37.380 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:32:37.395 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 16:32:37.395 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_a_grade_employees，第1页，每页50条
2025-06-27 16:32:37.395 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_a_grade_employees 第1页
2025-06-27 16:32:37.395 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:32:37.395 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_a_grade_employees 第1页数据，每页50条
2025-06-27 16:32:37.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:32:37.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 16:32:37.411 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:32:37.415 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 16:32:37.416 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 16:32:37.417 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 16:32:37.422 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:32:37.423 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:32:37.425 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 16:32:37.426 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:32:37.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:32:37.435 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 16:32:37.435 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:32:37.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:32:37.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:32:37.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 30.13ms
2025-06-27 16:32:37.459 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:32:37.459 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 16:32:42.619 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 16:32:42.619 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:32:42.619 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:32:42.619 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:32:42.619 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 16:32:42.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 12
2025-06-27 16:32:42.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-27 16:32:42.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:32:42.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-27 16:32:42.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 15.69ms
2025-06-27 16:32:42.635 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-27 16:32:42.635 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 16:32:44.270 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年', '工资表']
2025-06-27 16:32:44.270 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:32:44.270 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:32:44.270 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:32:44.270 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:32:44.270 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:32:44.270 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:32:44.270 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:32:44.286 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_active_employees 第1页
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_active_employees 第1页数据，每页50条
2025-06-27 16:32:44.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:32:44.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 16:32:44.286 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 16:32:44.301 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 16:32:44.301 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:32:44.301 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:32:44.301 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:32:44.301 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:32:44.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 12 -> 50
2025-06-27 16:32:44.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:32:44.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:32:44.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:32:44.337 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.03ms
2025-06-27 16:32:44.340 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:32:44.340 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:32:44.348 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:32:47.550 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-27 16:32:47.550 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-27 16:32:51.176 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年']
2025-06-27 16:32:51.176 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:32:51.192 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:32:51.192 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:32:51.192 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:32:51.192 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:32:51.192 | WARNING  | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:507 | 检测到表格 table_1_1491289383440 的表头重影: ['工号', 'id']
2025-06-27 16:32:51.192 | WARNING  | src.gui.table_header_manager:_clear_single_table_header:196 | 检测到表格 table_1_1491289383440 存在表头重影: ['工号', 'id']
2025-06-27 16:32:51.192 | WARNING  | src.gui.prototype.prototype_main_window:_on_header_shadow_detected:3469 | 检测到表格 table_1_1491289383440 表头重影: ['工号', 'id']
2025-06-27 16:32:51.277 | ERROR    | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:525 | 表格 table_1_1491289383440 表头重影修复失败，仍有重影: ['工号', 'id']
2025-06-27 16:32:51.277 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 85.52ms
2025-06-27 16:32:51.277 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：1 个重影表格，0 个修复成功，1 个修复失败
2025-06-27 16:32:51.277 | INFO     | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3393 | 导航切换预清理：修复了 0 个表格的表头重影
2025-06-27 16:32:51.277 | WARNING  | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3395 | 导航切换预清理：1 个表格修复失败
2025-06-27 16:32:51.277 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:32:51.277 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 16:32:51.277 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:32:51.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_pension_employees 获取数据...
2025-06-27 16:32:51.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_pension_employees 获取 13 行数据。
2025-06-27 16:32:51.277 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 5 个字段重命名
2025-06-27 16:32:51.277 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:32:51.277 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 16:32:51.300 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 16:32:51.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 16:32:51.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 16:32:51.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:32:51.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 16:32:51.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 8.83ms
2025-06-27 16:32:51.309 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 16:32:53.719 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > 退休人员']
2025-06-27 16:32:53.719 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:32:53.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:32:53.719 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:32:53.719 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:32:53.719 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:32:53.719 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:32:53.719 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:32:53.719 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:32:53.719 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 16:32:53.719 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:32:53.719 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_retired_employees 获取数据...
2025-06-27 16:32:53.719 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_retired_employees 获取 2 行数据。
2025-06-27 16:32:53.719 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 3 个字段重命名
2025-06-27 16:32:53.719 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:32:53.719 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 16:32:53.735 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 16:32:53.735 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 16:32:53.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 16:32:53.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 16:32:53.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:32:53.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 16:32:53.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 0.00ms
2025-06-27 16:32:53.735 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 16:32:58.872 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > 退休人员']
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:32:58.887 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:32:58.887 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:32:58.887 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:32:58.887 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:32:58.887 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:32:58.887 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_a_grade_employees，第1页，每页50条
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_a_grade_employees 第1页
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_a_grade_employees 无需字段重命名
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-27 16:32:58.887 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 16:32:58.887 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:32:58.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 50
2025-06-27 16:32:58.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:32:58.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:32:58.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:32:58.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 32.82ms
2025-06-27 16:32:58.920 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:32:58.924 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 16:32:58.928 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:33:06.567 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > 退休人员']
2025-06-27 16:33:06.567 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:33:06.567 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:33:06.567 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:33:06.567 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:33:06.567 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:33:06.567 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:33:06.567 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:33:06.567 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:33:06.567 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:33:06.567 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_active_employees 第1页
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_active_employees 无需字段重命名
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 16:33:06.583 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:33:06.583 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:33:06.610 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 27.77ms
2025-06-27 16:33:06.610 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:33:06.610 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:33:06.616 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:33:09.003 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 16:33:09.003 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:33:09.003 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:33:09.003 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:33:09.003 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:33:09.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:33:09.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:33:09.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:33:09.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:33:09.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 73.81ms
2025-06-27 16:33:09.076 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 16:33:09.077 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 16:33:13.280 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 1月', '工资表 > 2027年 > 5月 > 离休人员']
2025-06-27 16:33:13.280 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 1月
2025-06-27 16:33:13.280 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:33:13.280 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:33:13.280 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:33:13.280 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:33:13.280 | WARNING  | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:507 | 检测到表格 table_1_1491289383440 的表头重影: ['工号', 'id']
2025-06-27 16:33:13.280 | WARNING  | src.gui.table_header_manager:_clear_single_table_header:196 | 检测到表格 table_1_1491289383440 存在表头重影: ['工号', 'id']
2025-06-27 16:33:13.280 | WARNING  | src.gui.prototype.prototype_main_window:_on_header_shadow_detected:3469 | 检测到表格 table_1_1491289383440 表头重影: ['工号', 'id']
2025-06-27 16:33:13.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:519 | 表格 table_1_1491289383440 表头重影修复成功
2025-06-27 16:33:13.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 92.78ms
2025-06-27 16:33:13.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：1 个重影表格，1 个修复成功，0 个修复失败
2025-06-27 16:33:13.373 | INFO     | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3393 | 导航切换预清理：修复了 1 个表格的表头重影
2025-06-27 16:33:13.373 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:33:13.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:33:13.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:33:13.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:33:13.388 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:33:13.388 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:33:13.388 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:33:13.388 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 1月
2025-06-27 16:33:14.675 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 1月', '工资表 > 2027年 > 1月 > A岗职工']
2025-06-27 16:33:14.675 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 1月 > A岗职工
2025-06-27 16:33:14.675 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:33:14.675 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:33:14.675 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:33:14.691 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:33:14.691 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:33:14.691 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:33:14.691 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_01_a_grade_employees，第1页，每页50条
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_01_a_grade_employees 第1页
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 1月 > A岗职工
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_01_a_grade_employees 第1页数据，每页50条
2025-06-27 16:33:14.691 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:33:14.691 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_01_a_grade_employees 没有字段映射配置
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 16:33:14.691 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 16:33:14.706 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:33:14.706 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:33:14.706 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_01_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 16:33:14.706 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:33:14.706 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:33:14.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:33:14.726 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 16:33:14.728 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:33:14.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 46.85ms
2025-06-27 16:33:14.754 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:33:14.754 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 16:33:16.464 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 1月', '工资表 > 2027年 > 1月 > 全部在职人员']
2025-06-27 16:33:16.464 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 16:33:16.464 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:33:16.464 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:33:16.464 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:33:16.464 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:33:16.464 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:33:16.464 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:33:16.464 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_01_active_employees，第1页，每页50条
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_01_active_employees 第1页
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_01_active_employees 第1页数据，每页50条
2025-06-27 16:33:16.480 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:33:16.480 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_01_active_employees 没有字段映射配置
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 16:33:16.480 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 16:33:16.495 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:33:16.495 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:33:16.495 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_01_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:33:16.495 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:33:16.515 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:33:16.518 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:33:16.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:33:16.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:33:16.553 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 57.20ms
2025-06-27 16:33:16.554 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:33:16.555 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:33:17.780 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 1月', '工资表 > 2027年 > 1月 > 退休人员']
2025-06-27 16:33:17.780 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 1月 > 退休人员
2025-06-27 16:33:17.780 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:33:17.780 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:33:17.780 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:33:17.780 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:33:17.780 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:33:17.780 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:33:17.780 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:33:17.780 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 16:33:17.780 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 1月 > 退休人员
2025-06-27 16:33:17.780 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_01_pension_employees 获取数据...
2025-06-27 16:33:17.780 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_01_pension_employees 获取 13 行数据。
2025-06-27 16:33:17.780 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_01_pension_employees 没有字段映射配置
2025-06-27 16:33:17.780 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:33:17.796 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_01_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 16:33:17.796 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 16:33:17.796 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 16:33:17.796 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 16:33:17.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 16:33:17.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 26.67ms
2025-06-27 16:33:17.823 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 16:33:18.839 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 1月', '工资表 > 2027年 > 1月 > 离休人员']
2025-06-27 16:33:18.840 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 1月 > 离休人员
2025-06-27 16:33:18.841 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1491289588656 已经注册，将覆盖现有注册
2025-06-27 16:33:18.842 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1491289383440 已经注册，将覆盖现有注册
2025-06-27 16:33:18.842 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:33:18.843 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:33:18.843 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:33:18.844 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:33:18.845 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:33:18.846 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 16:33:18.847 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 1月 > 离休人员
2025-06-27 16:33:18.848 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_01_retired_employees 获取数据...
2025-06-27 16:33:18.851 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_01_retired_employees 获取 2 行数据。
2025-06-27 16:33:18.854 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_01_retired_employees 没有字段映射配置
2025-06-27 16:33:18.856 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:33:18.856 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_01_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 16:33:18.858 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 16:33:18.862 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 16:33:18.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 16:33:18.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 16:33:18.865 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 16:33:18.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 10.78ms
2025-06-27 16:33:18.871 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 16:33:22.433 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-27 16:37:08.460 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 16:37:08.460 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 16:37:08.460 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 16:37:08.460 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 16:37:08.460 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 16:37:08.460 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 16:37:10.836 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 16:37:10.836 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 16:37:10.836 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:37:10.836 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:37:10.836 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 16:37:10.836 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 16:37:10.836 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 16:37:10.878 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 16:37:10.879 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 16:37:10.881 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:37:10.881 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 16:37:10.881 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 16:37:10.881 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 16:37:10.885 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 16:37:10.885 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 16:37:11.602 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1518 | 菜单栏创建完成
2025-06-27 16:37:11.602 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:37:11.602 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:37:11.602 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 16:37:11.618 | INFO     | src.gui.prototype.prototype_main_window:__init__:1494 | 菜单栏管理器初始化完成
2025-06-27 16:37:11.618 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 16:37:11.618 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2195 | 管理器设置完成，包含增强版表头管理器
2025-06-27 16:37:11.618 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-27 16:37:11.618 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 16:37:11.618 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 16:37:11.638 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 16:37:11.638 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 16:37:11.644 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 16:37:11.648 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 8个展开项
2025-06-27 16:37:11.650 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-06-27 16:37:11.653 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 16:37:11.733 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 16:37:11.733 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 16:37:11.735 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:37:11.735 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 16:37:11.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 16:37:11.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:37:11.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:37:11.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.72ms
2025-06-27 16:37:11.746 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:37:11.756 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 16:37:11.814 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 16:37:11.863 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:37:11.864 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:37:11.867 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2168 | 快捷键设置完成
2025-06-27 16:37:11.867 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2157 | 主窗口UI设置完成。
2025-06-27 16:37:11.867 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2223 | 信号连接设置完成
2025-06-27 16:37:11.869 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:37:11.871 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2652 | 已加载字段映射信息，共4个表的映射
2025-06-27 16:37:11.871 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:37:11.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:37:11.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:37:11.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.70ms
2025-06-27 16:37:11.881 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:37:11.885 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:37:11.908 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:37:11.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:37:11.911 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:37:11.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.06ms
2025-06-27 16:37:11.913 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:37:11.915 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:37:11.916 | INFO     | src.gui.prototype.prototype_main_window:__init__:2109 | 原型主窗口初始化完成
2025-06-27 16:37:12.332 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 16:37:12.338 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 16:37:12.339 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1249 | MainWorkspaceArea 响应式适配: sm
2025-06-27 16:37:12.342 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 16:37:12.343 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 16:37:12.442 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 16:37:12.461 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 16:37:12.463 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 16:37:13.095 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 16:37:13.096 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 5 个月份
2025-06-27 16:37:13.097 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 16:37:13.098 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 16:37:13.099 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 16:37:13.100 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 16:37:13.100 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 16:37:13.101 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 16:37:13.101 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 16:37:13.102 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 16:37:13.103 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 16:37:13.103 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 16:37:13.104 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 16:37:13.105 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 29 个月份
2025-06-27 16:37:13.106 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 16:37:13.107 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 16:37:13.309 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 16:37:13.309 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 16:37:13.340 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 16:37:13.340 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:37:13.340 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:37:13.340 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:37:13.340 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:37:13.340 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:37:13.340 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:37:13.355 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 15.18ms
2025-06-27 16:37:13.357 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:37:13.359 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2743 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 16:37:13.363 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:37:13.366 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:37:13.368 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:37:13.372 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_active_employees 第1页
2025-06-27 16:37:13.384 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_active_employees 第1页数据，每页50条
2025-06-27 16:37:13.385 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:37:13.397 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 16:37:13.398 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:37:13.403 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:37:13.404 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 16:37:13.406 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 16:37:13.414 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:37:13.416 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:37:13.428 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:37:13.430 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:37:13.441 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:37:13.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 16:37:13.455 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:37:13.460 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:37:13.462 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:37:13.523 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 74.80ms
2025-06-27 16:37:13.524 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:37:13.524 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:38:03.627 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 16:38:03.627 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:03.627 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:38:03.627 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:38:03.643 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:38:03.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:38:03.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:03.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:03.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:03.659 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.67ms
2025-06-27 16:38:03.659 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 16:38:03.659 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 16:38:07.380 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 16:38:07.380 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月
2025-06-27 16:38:07.380 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:07.380 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:07.380 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:07.395 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:07.395 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:38:07.395 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:07.395 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:38:07.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:38:07.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:38:07.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:38:07.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.67ms
2025-06-27 16:38:07.411 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:38:07.411 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:07.411 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:38:07.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:38:07.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:38:07.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:38:07.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 17.12ms
2025-06-27 16:38:07.432 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:38:07.438 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:07.441 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月
2025-06-27 16:38:09.724 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 16:38:09.724 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:38:09.724 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:09.724 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:09.724 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:09.724 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:09.740 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 16.19ms
2025-06-27 16:38:09.740 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:09.740 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:09.740 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:38:09.740 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:38:09.740 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_active_employees 第1页
2025-06-27 16:38:09.740 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_active_employees 无需字段重命名
2025-06-27 16:38:09.740 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 16:38:09.740 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:09.740 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:38:09.740 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:38:09.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:38:09.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:09.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:09.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:09.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.70ms
2025-06-27 16:38:09.772 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:38:09.775 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:38:09.775 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:38:13.457 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 16:38:13.472 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:13.472 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:38:13.472 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:38:13.472 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:38:13.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:38:13.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:13.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:13.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:13.500 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 27.49ms
2025-06-27 16:38:13.507 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 16:38:13.511 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 16:38:15.156 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年', '工资表']
2025-06-27 16:38:15.156 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:38:15.156 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:15.156 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:15.171 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:15.171 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:15.171 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:38:15.171 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:15.171 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:15.171 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 16:38:15.171 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_a_grade_employees，第1页，每页50条
2025-06-27 16:38:15.171 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_a_grade_employees 第1页
2025-06-27 16:38:15.171 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:38:15.171 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_a_grade_employees 第1页数据，每页50条
2025-06-27 16:38:15.171 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:38:15.171 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 16:38:15.187 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:38:15.187 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:38:15.187 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 16:38:15.187 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 16:38:15.187 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:38:15.187 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:15.202 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 16:38:15.205 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:38:15.211 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:38:15.214 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 16:38:15.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:15.242 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:15.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:15.264 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 56.83ms
2025-06-27 16:38:15.264 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:38:15.264 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 16:38:21.801 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 16:38:21.801 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:21.802 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:38:21.803 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:38:21.804 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 16:38:21.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 12
2025-06-27 16:38:21.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-27 16:38:21.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:21.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-27 16:38:21.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 13.85ms
2025-06-27 16:38:21.821 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-27 16:38:21.823 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 16:38:24.534 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年']
2025-06-27 16:38:24.536 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:38:24.538 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:24.538 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:24.539 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:24.539 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:24.540 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.02ms
2025-06-27 16:38:24.540 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:24.542 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:24.542 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 16:38:24.543 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:38:24.543 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_pension_employees 获取数据...
2025-06-27 16:38:24.544 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_pension_employees 获取 13 行数据。
2025-06-27 16:38:24.558 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:38:24.573 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:24.580 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 16:38:24.583 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 16:38:24.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 12 -> 13
2025-06-27 16:38:24.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 16:38:24.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:24.591 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 16:38:24.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 17.71ms
2025-06-27 16:38:24.604 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 16:38:26.327 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > 退休人员']
2025-06-27 16:38:26.327 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:38:26.327 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:26.327 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:26.327 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:26.327 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:26.327 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:38:26.327 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:26.327 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:26.327 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 16:38:26.327 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:38:26.327 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_retired_employees 获取数据...
2025-06-27 16:38:26.327 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_retired_employees 获取 2 行数据。
2025-06-27 16:38:26.327 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:38:26.327 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:26.327 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 16:38:26.356 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 16:38:26.363 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 16:38:26.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 16:38:26.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 16:38:26.385 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:26.385 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 16:38:26.394 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 34.72ms
2025-06-27 16:38:26.395 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > 退休人员']
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:38:28.148 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:28.148 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:28.148 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:28.148 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:38:28.148 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:28.148 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_active_employees 第1页
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_active_employees 无需字段重命名
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 16:38:28.148 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:38:28.148 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:38:28.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 50
2025-06-27 16:38:28.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:28.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:38:28.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:28.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.66ms
2025-06-27 16:38:28.179 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:38:28.179 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:38:28.179 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:38:30.338 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 5月 > 离休人员']
2025-06-27 16:38:30.338 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 2月
2025-06-27 16:38:30.338 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:30.338 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:30.338 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:30.338 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:30.338 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:38:30.338 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:30.338 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:38:30.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:38:30.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:38:30.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:38:30.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.66ms
2025-06-27 16:38:30.353 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:38:30.353 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:30.353 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:38:30.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:38:30.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:38:30.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:38:30.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.67ms
2025-06-27 16:38:30.369 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:38:30.369 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:30.369 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 2月 > A岗职工']
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 2月 > A岗职工
2025-06-27 16:38:32.297 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:32.297 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:32.297 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:32.297 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:38:32.297 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:32.297 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_02_a_grade_employees，第1页，每页50条
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_02_a_grade_employees 第1页
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > A岗职工
2025-06-27 16:38:32.297 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_02_a_grade_employees 第1页数据，每页50条
2025-06-27 16:38:32.297 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:38:32.312 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 16:38:32.312 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:38:32.312 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_02_a_grade_employees 没有字段映射配置
2025-06-27 16:38:32.312 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 16:38:32.312 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 16:38:32.312 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:38:32.312 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:32.312 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_02_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 16:38:32.328 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:38:32.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:38:32.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:32.328 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 16:38:32.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:32.494 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 165.80ms
2025-06-27 16:38:32.495 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:38:32.496 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 16:38:34.131 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 2月 > 全部在职人员']
2025-06-27 16:38:34.131 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 2月 > 全部在职人员
2025-06-27 16:38:34.131 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2123460002736 已经注册，将覆盖现有注册
2025-06-27 16:38:34.131 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2123459797520 已经注册，将覆盖现有注册
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:38:34.146 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:38:34.146 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:38:34.146 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:38:34.146 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_02_active_employees，第1页，每页50条
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_02_active_employees 第1页
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > 全部在职人员
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_02_active_employees 第1页数据，每页50条
2025-06-27 16:38:34.146 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:38:34.146 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_02_active_employees 没有字段映射配置
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 16:38:34.146 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 16:38:34.187 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:38:34.188 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:34.202 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:38:34.205 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_02_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:38:34.216 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:38:34.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:38:34.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:34.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:34.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 38.74ms
2025-06-27 16:38:34.284 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:38:34.284 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:38:37.653 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 16:38:37.653 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:38:37.653 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:38:37.653 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:38:37.653 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:38:37.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:38:37.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:38:37.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:38:37.669 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.18ms
2025-06-27 16:38:37.669 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 16:38:37.669 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 16:38:48.287 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-27 16:58:53.909 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 16:58:53.909 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 16:58:53.909 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 16:58:53.909 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 16:58:53.909 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 16:58:53.925 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 16:58:56.843 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 16:58:56.843 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 16:58:56.844 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:58:56.844 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:58:56.846 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 16:58:56.846 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 16:58:56.847 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 16:58:56.902 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 16:58:56.904 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 16:58:56.906 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:58:56.910 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 16:58:56.914 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 16:58:56.936 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 16:58:56.940 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 16:58:56.940 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 16:58:57.656 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1518 | 菜单栏创建完成
2025-06-27 16:58:57.658 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:58:57.658 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:58:57.662 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 16:58:57.664 | INFO     | src.gui.prototype.prototype_main_window:__init__:1494 | 菜单栏管理器初始化完成
2025-06-27 16:58:57.664 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 16:58:57.664 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2195 | 管理器设置完成，包含增强版表头管理器
2025-06-27 16:58:57.709 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-27 16:58:57.711 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 16:58:57.771 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 16:58:57.784 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 16:58:57.786 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 16:58:57.799 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 16:58:57.801 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 9个展开项
2025-06-27 16:58:57.802 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-06-27 16:58:57.803 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 16:58:57.905 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 16:58:57.906 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 16:58:57.908 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:58:57.909 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 16:58:57.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 16:58:57.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:58:57.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:58:57.921 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.79ms
2025-06-27 16:58:57.923 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:58:57.934 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 16:58:58.016 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 16:58:58.037 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 16:58:58.038 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 16:58:58.039 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2168 | 快捷键设置完成
2025-06-27 16:58:58.040 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2157 | 主窗口UI设置完成。
2025-06-27 16:58:58.040 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2223 | 信号连接设置完成
2025-06-27 16:58:58.041 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:58:58.043 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2652 | 已加载字段映射信息，共4个表的映射
2025-06-27 16:58:58.043 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:58:58.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:58:58.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:58:58.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.12ms
2025-06-27 16:58:58.048 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:58:58.049 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:58:58.049 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:58:58.050 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 16:58:58.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:58:58.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.03ms
2025-06-27 16:58:58.053 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:58:58.053 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:58:58.054 | INFO     | src.gui.prototype.prototype_main_window:__init__:2109 | 原型主窗口初始化完成
2025-06-27 16:58:58.503 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 16:58:58.509 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 16:58:58.510 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1249 | MainWorkspaceArea 响应式适配: sm
2025-06-27 16:58:58.512 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 16:58:58.513 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 16:58:58.587 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 16:58:58.588 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 16:58:58.589 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 16:58:59.227 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 16:58:59.228 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 5 个月份
2025-06-27 16:58:59.229 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 16:58:59.230 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 16:58:59.231 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 16:58:59.232 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 16:58:59.233 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 16:58:59.233 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 16:58:59.234 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 16:58:59.234 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 16:58:59.235 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 16:58:59.235 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 16:58:59.236 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 16:58:59.236 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 29 个月份
2025-06-27 16:58:59.237 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 16:58:59.238 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 16:58:59.439 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 16:58:59.439 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 16:58:59.480 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 116 个匹配类型 'salary_data' 的表
2025-06-27 16:58:59.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:58:59.489 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:58:59.496 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:58:59.504 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 05月 > 全部在职人员
2025-06-27 16:58:59.508 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:58:59.509 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:58:59.510 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.04ms
2025-06-27 16:58:59.511 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:58:59.511 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2743 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 16:58:59.512 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:58:59.513 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:58:59.514 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:58:59.514 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_active_employees 第1页
2025-06-27 16:58:59.515 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_active_employees 第1页数据，每页50条
2025-06-27 16:58:59.517 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:58:59.541 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 16:58:59.545 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:58:59.562 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:58:59.563 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 16:58:59.604 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 16:58:59.617 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:58:59.618 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:58:59.631 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 16:58:59.633 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:58:59.646 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:58:59.655 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 16:58:59.657 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:58:59.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:58:59.666 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:58:59.723 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 71.27ms
2025-06-27 16:58:59.735 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:58:59.736 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:59:11.144 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 16:59:11.145 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月
2025-06-27 16:59:11.147 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:11.148 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:11.148 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:11.149 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:11.150 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.00ms
2025-06-27 16:59:11.151 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:11.151 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:59:11.182 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:59:11.187 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:59:11.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:59:11.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 41.78ms
2025-06-27 16:59:11.195 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:59:11.198 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:11.198 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 16:59:11.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 16:59:11.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 7 个表头
2025-06-27 16:59:11.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 16:59:11.208 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 9.38ms
2025-06-27 16:59:11.210 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 16:59:11.211 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:11.211 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月
2025-06-27 16:59:15.544 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 16:59:15.545 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:59:15.547 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:15.547 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:15.548 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:15.548 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:15.549 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.04ms
2025-06-27 16:59:15.550 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:15.550 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:15.551 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 16:59:15.551 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_a_grade_employees，第1页，每页50条
2025-06-27 16:59:15.552 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_05_a_grade_employees 第1页
2025-06-27 16:59:15.552 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:59:15.552 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_05_a_grade_employees 第1页数据，每页50条
2025-06-27 16:59:15.553 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 16:59:15.558 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 16:59:15.558 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 16:59:15.559 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:59:15.560 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 16:59:15.562 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 16:59:15.568 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 16:59:15.569 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:15.571 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:15.571 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:59:15.578 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:59:15.578 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:59:15.581 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 16:59:15.581 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:15.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:59:15.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 43.40ms
2025-06-27 16:59:15.621 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:59:15.625 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 16:59:20.224 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年', '工资表']
2025-06-27 16:59:20.224 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:59:20.224 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:20.224 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:20.224 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:20.224 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:20.224 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:20.224 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:20.224 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:20.224 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_active_employees 第1页
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_active_employees 无需字段重命名
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 16:59:20.240 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:20.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:59:20.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.71ms
2025-06-27 16:59:20.255 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:59:20.255 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:59:20.255 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:59:22.853 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年']
2025-06-27 16:59:22.853 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:59:22.868 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:22.871 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:22.871 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:22.871 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:22.871 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:22.871 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:22.871 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:22.871 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 16:59:22.871 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:59:22.871 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_pension_employees 获取数据...
2025-06-27 16:59:22.871 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_pension_employees 获取 13 行数据。
2025-06-27 16:59:22.871 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:59:22.871 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:22.871 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:22.893 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 16:59:22.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 16:59:22.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 16:59:22.909 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:22.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 16:59:22.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 23.68ms
2025-06-27 16:59:22.934 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 16:59:26.782 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > 退休人员']
2025-06-27 16:59:26.782 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:59:26.782 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:26.782 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:26.782 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:26.782 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:26.782 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:26.782 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:26.782 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:26.799 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 16:59:26.799 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:59:26.799 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_retired_employees 获取数据...
2025-06-27 16:59:26.799 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_retired_employees 获取 2 行数据。
2025-06-27 16:59:26.799 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:59:26.799 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:26.799 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:26.799 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 16:59:26.813 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 16:59:26.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 16:59:26.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 16:59:26.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:26.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 16:59:26.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 14.21ms
2025-06-27 16:59:26.813 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 离休人员']
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:59:29.825 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:29.825 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:29.825 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:29.825 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:29.825 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:29.825 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:59:29.825 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_pension_employees 获取数据...
2025-06-27 16:59:29.825 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_pension_employees 获取 13 行数据。
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:59:29.825 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:29.825 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 16:59:29.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 13
2025-06-27 16:59:29.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 16:59:29.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:29.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 16:59:29.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-27 16:59:29.841 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月 > 离休人员']
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:59:32.257 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:32.257 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:32.257 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:32.257 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:32.257 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:32.257 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_active_employees 第1页
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_active_employees 无需字段重命名
2025-06-27 16:59:32.257 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 16:59:32.257 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:32.274 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:32.274 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:59:32.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 50
2025-06-27 16:59:32.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:59:32.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:32.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:59:32.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 27.35ms
2025-06-27 16:59:32.303 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:59:32.309 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:59:32.317 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:59:38.057 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > A岗职工']
2025-06-27 16:59:38.073 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:59:38.073 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:38.073 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:38.073 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:38.073 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:38.073 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:38.073 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:38.073 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:38.073 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(2条)，使用普通模式
2025-06-27 16:59:38.073 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 离休人员
2025-06-27 16:59:38.073 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_retired_employees 获取数据...
2025-06-27 16:59:38.073 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_retired_employees 获取 2 行数据。
2025-06-27 16:59:38.073 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:59:38.073 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:38.073 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:38.089 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 16:59:38.089 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 16:59:38.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 2
2025-06-27 16:59:38.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 16:59:38.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:38.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 16:59:38.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 0.00ms
2025-06-27 16:59:38.104 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 16:59:44.153 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工']
2025-06-27 16:59:44.153 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:59:44.153 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:44.153 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:44.153 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:44.153 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:44.153 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:44.153 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:44.153 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:44.153 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 16:59:44.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 退休人员
2025-06-27 16:59:44.153 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_05_pension_employees 获取数据...
2025-06-27 16:59:44.153 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_05_pension_employees 获取 13 行数据。
2025-06-27 16:59:44.153 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 16 个字段重命名
2025-06-27 16:59:44.153 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:44.173 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:44.173 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 16:59:44.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 13
2025-06-27 16:59:44.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 16:59:44.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:44.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 16:59:44.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 11.25ms
2025-06-27 16:59:44.184 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 16:59:52.798 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月', '工资表 > 2027年 > 5月 > A岗职工']
2025-06-27 16:59:52.798 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:59:52.798 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:52.798 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:52.798 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:52.798 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:52.798 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:52.798 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:52.814 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_active_employees，第1页，每页50条
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_active_employees 第1页
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_active_employees 无需字段重命名
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 16:59:52.814 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_active_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 50
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:52.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:59:52.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-27 16:59:52.829 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:59:52.829 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 16:59:52.845 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > 全部在职人员
2025-06-27 16:59:56.057 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 5月 > 退休人员', '工资表 > 2027年 > 5月 > 全部在职人员', '工资表 > 2027年 > 5月 > 离休人员', '工资表 > 2027年 > 5月 > A岗职工', '工资表 > 2027年 > 5月']
2025-06-27 16:59:56.057 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 16:59:56.073 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2352731407280 已经注册，将覆盖现有注册
2025-06-27 16:59:56.073 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2352731202064 已经注册，将覆盖现有注册
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 16:59:56.073 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 16:59:56.073 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 16:59:56.073 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 16:59:56.073 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_05_a_grade_employees，第1页，每页50条
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_05_a_grade_employees 第1页
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_05_a_grade_employees 无需字段重命名
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-27 16:59:56.073 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 16:59:56.073 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 16:59:56.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 16:59:56.091 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 16:59:56.091 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 16:59:56.091 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 16:59:56.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 47.91ms
2025-06-27 16:59:56.122 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 16:59:56.122 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 16:59:56.128 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 5月 > A岗职工
2025-06-27 17:00:13.303 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-27 17:10:09.339 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 17:10:09.340 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 17:10:09.340 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 17:10:09.340 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 17:10:09.341 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 17:10:09.341 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 17:10:09.915 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 17:10:09.915 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 17:10:09.916 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 17:10:09.916 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 17:10:09.917 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 17:10:09.928 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 17:10:09.928 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 17:10:09.929 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 17:10:09.930 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
